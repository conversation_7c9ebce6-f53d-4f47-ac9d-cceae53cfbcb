/// <reference types="vite-plugin-svgr/client" />
import { styled } from "@linaria/react";
import { useQuery, useQueryClient } from "react-query";
import { Empty } from "antd";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { LoadingOutlined } from "@ant-design/icons";
import {
  ATTRIBUTES_WITH_NO_MANDATORY,
  GET_ALL_TEMPLATES_KEY,
  GET_HEADER_MENUS,
  GET_NODE_ATTRIBUTES_DETAILS,
  METAATTRIBUTE_ID_ALLOWEDLINKS,
  METAATTRIBUTE_ID_ATRIBUTETYPE,
  METAATTRIBUTE_ID_ATTRIBUTES_LOOKUP,
  METAATTRIBUTE_ID_COMPOUND_TEMPLATE1,
  METAATTRIBUTE_ID_COMPOUND_TEMPLATE2,
  METAATTRIBUTE_ID_COMPOUND_VARIANT,
  METAATTRIBUTE_ID_COMPOUND_VISUALIZATION,
  METAATTRIBUTE_ID_DEFAULT_VALUES,
  METAATTRIBUTE_ID_DISPLAY_OPTIONS,
  METAATTRIBUTE_ID_DROPDOWNITEMS,
  METAATTRIBUTE_ID_FILTER_MENUS,
  METAATTRIBUTE_ID_FILTER_TEMPLATES,
  METAATTRIBUTE_ID_HELP,
  METAATTRIBUTE_ID_ISMANDATORY,
  METAATTRIBUTE_ID_LIST1,
  METAATTRIBUTE_ID_LIST2,
  METAATTRIBUTE_ID_MULTIPLICITY,
  METAATTRIBUTE_ID_MULTIPLICITY_LIST1,
  METAATTRIBUTE_ID_MULTIPLICITY_LIST2,
  METAATTRIBUTE_ID_PARENT1,
  METAATTRIBUTE_ID_PARENT1_PREVIEW,
  METAATTRIBUTE_ID_PARENT2,
  METAATTRIBUTE_ID_PARENT2_PREVIEW,
  METAATTRIBUTE_ID_REGEX,
  METAATTRIBUTE_ID_RELATION_HETEROGENIC,
  METAATTRIBUTE_ID_RELATION_VISUALIZATION,
  METAATTRIBUTE_ID_SOURCE_DATA,
  TEMP_GROUPING_NODE_ID,
  TEMPLATES_ATTRIBUTE_TEMPLATE_ID,
} from "../../../../constants";
import {
  I_ATTRIBUTE_TYPES,
  IAttributes,
  INodeDetails,
  ITemplates,
} from "../../../../interfaces";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../store";
import { deepClone, getAttributeTitleWidth } from "../../../../utils";
import {
  getNodeDetails,
  getParentNameDetails,
} from "../../../../services/node";
import { AttributeItem } from "../../../atoms";
import { setWorkingVersionMask } from "../../../../store/features";
import { API } from "../../../../utils/api";
import {
  COMPOUND_VARIANT,
  COMPOUND_VISUALIZATION,
} from "../../../../constants/dropdownOptions";
import { getSourceForLifecycle } from "../../../../services/attribute";
import { debounce, isEqual } from "lodash";
import { usePermissions } from "../../../../utils/functions/customHooks";

const MANDATORY_ATTRIBUTE_IDS = [
  METAATTRIBUTE_ID_FILTER_MENUS,
  METAATTRIBUTE_ID_SOURCE_DATA,
  METAATTRIBUTE_ID_ALLOWEDLINKS,
  METAATTRIBUTE_ID_MULTIPLICITY,
  METAATTRIBUTE_ID_LIST1,
  METAATTRIBUTE_ID_LIST2,
  METAATTRIBUTE_ID_MULTIPLICITY_LIST1,
  METAATTRIBUTE_ID_MULTIPLICITY_LIST2,
  METAATTRIBUTE_ID_COMPOUND_VISUALIZATION,
  METAATTRIBUTE_ID_DROPDOWNITEMS,
];

interface Props {
  attributes: any[];
  setAttributes: any;
  setEditingAttribute: any;
  editingAttribute: any;
  setMandatoryAttributes: any;
  mandatoryAttributes: any;
  setSpecialAttribute?: any;
  updateAllowedChildrens: (arg0: number[]) => void;
  id: string;
  isDraft: boolean;
}

const withRegexAttributes = ["editor", "number", "text", "textarea"];
const withDefaultValueAttributes = [
  "editor",
  "number",
  "text",
  "textarea",
  "date",
  "dateTime",
  "decimal",
  "link",
  "password",
  "iframe",
  "sql",
  "switch",
  "time",
];

const WorkingVersionArea = memo(
  ({
    attributes,
    setAttributes,
    editingAttribute,
    setEditingAttribute,
    setMandatoryAttributes,
    isDraft,
    mandatoryAttributes,
    setSpecialAttribute,
    updateAllowedChildrens,
    id,
  }: Props) => {
    const queryClient = useQueryClient();
    const { t } = useTranslation();
    const dispatch = useDispatch();

    const { getPermissions } = usePermissions();

    const [loading, setLoading] = useState(true);
    const [permissions, setPermissions] = useState([]);
    const [newAttributes, setNewAttributes] = useState([]);
    const [initialAttributes, setInitialAttributes] = useState([]);
    const [compoundParentId, setCompoundParentId] = useState(null);

    const { hiddenAttributes } = useSelector(
      (root: RootState) => root.globalSettings
    );
    const workingVersionActive = useSelector(
      (state: RootState) => state.mask.workingVersion
    );
    // const displaySaveButton = useSelector(
    //   (state: RootState) => state.mask.workingVersion
    // );
    const { trashCollapsed } = useSelector((state: RootState) => state.trash);

    const templatesData = useSelector(
      (state: RootState) => state.templatesStore.templates
    );

    const { isLoading, isError, error } = useQuery<INodeDetails, any>(
      [GET_NODE_ATTRIBUTES_DETAILS, id],
      () => getNodeDetails(id),
      {
        onSuccess: async (data) => {
          setNewAttributes([]);
          setAttributes([]);
          setPermissions(getPermissions(data?.permissionsId));

          const attributes = [];
          const mandatoryAttributes = [];
          const selectedTemplateAttributes =
            templatesData[data.templateId]?.attributeTemplates || [];
          let defaultValue = null;
          const attributeType = data?.body?.find(
            (attr) => attr.id === METAATTRIBUTE_ID_ATRIBUTETYPE
          )?.value;

          const defaultValueIndex = data.body?.findIndex(
            (attr) => attr.id === METAATTRIBUTE_ID_DEFAULT_VALUES
          );
          if (
            attributeType &&
            Object.keys(attributeType)[0] === "multipleSelect" &&
            defaultValueIndex !== -1
          )
            defaultValue = Object.keys(data.body[defaultValueIndex]?.value)[0];

          data.body?.forEach((attribute: IAttributes) => {
            const index = selectedTemplateAttributes?.findIndex(
              (item) => item.id == attribute.id
            );

            if (
              selectedTemplateAttributes[index]?.mandatory ||
              MANDATORY_ATTRIBUTE_IDS.includes(attribute.id)
            ) {
              mandatoryAttributes.push(attribute.id);
            }

            attributes.push({
              ...attribute,
              order: index,
              mandatory:
                selectedTemplateAttributes[index]?.mandatory ||
                MANDATORY_ATTRIBUTE_IDS.includes(attribute.id),
              help: selectedTemplateAttributes[index]?.help,
              regex: selectedTemplateAttributes[index]?.regex,
              items: selectedTemplateAttributes[index]?.items,
              value:
                attribute.type === "multiplicity"
                  ? {
                      text1: attribute?.value?.split("..")[0],
                      text2: attribute?.value?.split("..")[1],
                    }
                  : attribute.type === "switch"
                  ? attribute?.value || false
                  : attribute?.type === "dropdownItems"
                  ? Object.keys(attribute?.value || {})?.map((key) => {
                      return {
                        key: key,
                        name: attribute.value[key],
                        default: defaultValue === key || false,
                      };
                    })
                  : attribute?.value,
            });
          });

          setMandatoryAttributes([...mandatoryAttributes]);

          if (
            attributeType &&
            Object.keys(attributeType)[0] === "multipleSelect"
          ) {
            attributes.splice(defaultValueIndex, 1);
          }
          if (attributeType && Object.keys(attributeType)[0] === "compound") {
            // sort
            const preview1Index = attributes.findIndex(
              (item) => item.id === METAATTRIBUTE_ID_PARENT1_PREVIEW
            );
            attributes[preview1Index].readOnly = true;

            const parent1Index = attributes.findIndex(
              (item) => item.id === METAATTRIBUTE_ID_PARENT1
            );

            const preview2Index = attributes.findIndex(
              (item) => item.id === METAATTRIBUTE_ID_PARENT2_PREVIEW
            );
            attributes[preview2Index].readOnly = true;
            const parent2Index = attributes.findIndex(
              (item) => item.id === METAATTRIBUTE_ID_PARENT2
            );

            const parentDetails = await getParentNameDetails(
              [
                attributes[parent1Index]?.value,
                attributes[parent2Index]?.value,
              ]?.join()
            );

            attributes[preview1Index].value = parentDetails?.find(
              (attr) => attr.id == attributes[parent1Index]?.value
            )?.name;
            attributes[preview2Index].value = parentDetails?.find(
              (attr) => attr.id == attributes[parent2Index]?.value
            )?.name;

            if (parent1Index !== -1 && preview1Index !== -1) {
              const preview = attributes.splice(preview1Index, 1)[0];
              attributes.splice(parent1Index + 1, 0, preview);
            }

            if (parent2Index !== -1 && preview2Index !== -1) {
              const preview = attributes.splice(preview2Index, 1)[0];
              attributes.splice(parent2Index + 2, 0, preview);
            }
          }

          if (attributeType && Object.keys(attributeType)[0] === "relation") {
            const heterogic = attributes?.find(
              (attr) => attr.id === METAATTRIBUTE_ID_RELATION_HETEROGENIC
            )?.value;
            const allowedLinks = attributes?.find(
              (attr) => attr.id === METAATTRIBUTE_ID_ALLOWEDLINKS
            );
            const attributeLookup = attributes?.find(
              (attr) => attr.id === METAATTRIBUTE_ID_ATTRIBUTES_LOOKUP
            );
            allowedLinks["multiplicity"] = heterogic ? "1..n" : "1..1";
            if (attributeLookup) attributeLookup["multiplicity"] = "0..1";
          }
          setAttributes([...attributes]);
          setInitialAttributes(deepClone(attributes));
          setLoading(false);
        },
        onError: () => {
          setLoading(false);
        },
        enabled: !!id && !!templatesData && !isDraft,
      }
    );

    useEffect(() => {
      // for draft nodes
      if (isDraft) {
        setLoading(false);
        setAttributes([]);
        setNewAttributes([]);

        const selectedTemplate =
          templatesData[Number(TEMPLATES_ATTRIBUTE_TEMPLATE_ID)];
        if (selectedTemplate) {
          const mandatoryAttributes = [] as number[];

          const attributeTemplates = JSON.parse(
            JSON.stringify([...selectedTemplate.attributeTemplates])
          )?.filter(
            (attr: IAttributes) => !hiddenAttributes.includes(attr.name)
          );

          attributeTemplates?.forEach(
            (attribute: IAttributes, index: number) => {
              if (attribute?.mandatory) {
                mandatoryAttributes.push(attribute.id);
              }
              delete attribute.attributeId;
              attribute.id = attribute.id || index + 1;
              attribute.value =
                attribute.id === METAATTRIBUTE_ID_ISMANDATORY
                  ? false
                  : attribute.type === "multiplicity"
                  ? {
                      text1: attribute?.value?.split("..")[0],
                      text2: attribute?.value?.split("..")[1],
                    }
                  : attribute.type === "switch"
                  ? attribute?.value || false
                  : null;
            }
          );
          setMandatoryAttributes([...mandatoryAttributes]);
          setAttributes([...attributeTemplates]);
        }
      }
    }, [isDraft]);

    useEffect(() => {
      const titles = document.querySelectorAll(".attribute-title") as any;

      titles.forEach((title) => {
        title.style.width = `fit-content`;
      });

      const maxTitleWidth = getAttributeTitleWidth(".attribute-title");
      titles.forEach((title) => {
        title.style.width = `${maxTitleWidth}px`;
      });
    }, [attributes, trashCollapsed, newAttributes]);

    const setDebouncedParentId = useMemo(
      () =>
        debounce((val) => {
          setCompoundParentId(val);
        }, 500),
      []
    );

    const getCompoundParentPreview = async () => {
      let data = queryClient.getQueryData([
        GET_NODE_ATTRIBUTES_DETAILS,
        compoundParentId[0]?.toString(),
      ]) as INodeDetails;
      if (!data) {
        data = (await getNodeDetails(
          compoundParentId[0]?.toString()
        )) as INodeDetails;
        queryClient.setQueryData(
          [GET_NODE_ATTRIBUTES_DETAILS, compoundParentId[0]?.toString()],
          data
        );
      }

      const updatedAttributes = [...attributes];
      const index = updatedAttributes?.findIndex(
        (attr) =>
          attr.id ===
          (compoundParentId[1] === "parent1"
            ? METAATTRIBUTE_ID_PARENT1_PREVIEW
            : METAATTRIBUTE_ID_PARENT2_PREVIEW)
      );
      updatedAttributes[index].value = data?.name;
      setAttributes(updatedAttributes);
    };

    useEffect(() => {
      if (compoundParentId) {
        getCompoundParentPreview();
      }
    }, [compoundParentId]);

    const generateMetamodelAttributes = (value) => {
      const type = Object.keys(value || {})[0] as I_ATTRIBUTE_TYPES;
      const attributeTemplate =
        templatesData[TEMPLATES_ATTRIBUTE_TEMPLATE_ID]?.attributeTemplates ||
        [];
      const primaryAttributeIds = attributeTemplate?.map((attr) => attr.id);

      let allAttributes = [...attributes].filter((attr) =>
        primaryAttributeIds.includes(attr.id)
      );

      const indexOfAttributeTemplate = allAttributes.findIndex(
        (attr) => attr.id === METAATTRIBUTE_ID_ATRIBUTETYPE
      );

      allAttributes[indexOfAttributeTemplate].value = value;

      if (ATTRIBUTES_WITH_NO_MANDATORY.includes(type)) {
        allAttributes = allAttributes?.filter(
          (attr) => attr.id !== METAATTRIBUTE_ID_ISMANDATORY
        );
      } else {
        const isMandatoryPresent = allAttributes?.find(
          (attr) => attr.id === METAATTRIBUTE_ID_ISMANDATORY
        );
        if (!isMandatoryPresent) {
          const helpIndex = allAttributes?.findIndex(
            (attr) => attr.id === METAATTRIBUTE_ID_HELP
          );

          allAttributes.splice(helpIndex + 1, 0, {
            mandatory: false,
            name: "Is mandatory",
            type: "switch",
            value: false,
            id: METAATTRIBUTE_ID_ISMANDATORY,
          });
        }
      }

      if (type) {
        if (withDefaultValueAttributes.includes(type)) {
          const helpIndex = allAttributes?.findIndex(
            (attr) => attr.id === METAATTRIBUTE_ID_HELP
          );

          allAttributes.splice(helpIndex + 1, 0, {
            mandatory: false,
            name: "Default values",
            type: type,
            value: null,
            id: METAATTRIBUTE_ID_DEFAULT_VALUES,
          });
        }
      } else {
        allAttributes = allAttributes.filter(
          (item) => item.id !== METAATTRIBUTE_ID_DEFAULT_VALUES
        );
      }

      const isRegexPresent =
        attributes.findIndex((item) => item.type === "regex") !== -1;

      //for regex
      if (!isRegexPresent && withRegexAttributes.includes(type)) {
        const indexOfAttributeTemplate = attributes.findIndex(
          (attr) => attr.id === METAATTRIBUTE_ID_ATRIBUTETYPE
        );
        allAttributes.splice(indexOfAttributeTemplate + 1, 0, {
          id: METAATTRIBUTE_ID_REGEX,
          mandatory: false,
          name: "Regex",
          type: "regex",
          value: null,
        });
      } else if (isRegexPresent && !withRegexAttributes.includes(type)) {
        allAttributes = allAttributes.filter((item) => item.type !== "regex");
      }
      switch (type) {
        case "compound": {
          allAttributes.splice(indexOfAttributeTemplate + 1, 0, {
            id: METAATTRIBUTE_ID_COMPOUND_VARIANT,
            mandatory: true,
            name: "Source",
            type: "select",
            value: null,
          });

          allAttributes.splice(indexOfAttributeTemplate + 2, 0, {
            id: METAATTRIBUTE_ID_PARENT1,
            name: "Parent of list 1",
            type: "number",
            value: null,
          });
          allAttributes.splice(indexOfAttributeTemplate + 3, 0, {
            id: METAATTRIBUTE_ID_PARENT1_PREVIEW,
            name: "Preview of list 1",
            type: "text",
            value: null,
            readOnly: true,
          });
          allAttributes.splice(indexOfAttributeTemplate + 4, 0, {
            id: METAATTRIBUTE_ID_PARENT2,
            name: "Parent of list 2",
            type: "number",
            value: null,
          });
          allAttributes.splice(indexOfAttributeTemplate + 5, 0, {
            id: METAATTRIBUTE_ID_PARENT2_PREVIEW,
            name: "Preview of list 2",
            type: "text",
            value: null,
            readOnly: true,
          });

          allAttributes.splice(indexOfAttributeTemplate + 6, 0, {
            id: METAATTRIBUTE_ID_COMPOUND_TEMPLATE1,
            name: "Node templates 1",
            type: "allowedLinks",
            value: null,
          });

          allAttributes.splice(indexOfAttributeTemplate + 7, 0, {
            id: METAATTRIBUTE_ID_COMPOUND_TEMPLATE2,
            name: "Node templates 2",
            type: "allowedLinks",
            value: null,
          });

          allAttributes.splice(indexOfAttributeTemplate + 8, 0, {
            id: METAATTRIBUTE_ID_LIST1,
            name: "Name of List 1",
            type: "text",
            value: null,
            mandatory: true,
          });
          allAttributes.splice(indexOfAttributeTemplate + 9, 0, {
            id: METAATTRIBUTE_ID_LIST2,
            name: "Name of List 2",
            type: "text",
            value: null,
            mandatory: true,
          });
          allAttributes.splice(indexOfAttributeTemplate + 10, 0, {
            id: METAATTRIBUTE_ID_MULTIPLICITY_LIST1,
            name: "Multiplicity for List 1",
            type: "multiplicity",
            value: { text1: "", text2: "" },
            mandatory: true,
          });
          allAttributes.splice(indexOfAttributeTemplate + 11, 0, {
            id: METAATTRIBUTE_ID_MULTIPLICITY_LIST2,
            name: "Multiplicity for List 2",
            type: "multiplicity",
            value: { text1: "", text2: "" },
            mandatory: true,
          });
          allAttributes.splice(indexOfAttributeTemplate + 12, 0, {
            id: METAATTRIBUTE_ID_COMPOUND_VISUALIZATION,
            name: "Visualization",
            type: "select",
            value: null,
            mandatory: true,
          });
          setMandatoryAttributes([
            ...mandatoryAttributes,
            METAATTRIBUTE_ID_COMPOUND_VARIANT,
            METAATTRIBUTE_ID_LIST1,
            METAATTRIBUTE_ID_LIST2,
            METAATTRIBUTE_ID_MULTIPLICITY_LIST1,
            METAATTRIBUTE_ID_MULTIPLICITY_LIST2,
          ]);
          break;
        }

        case "compoundSimple":
        case "roleMatrix": {
          allAttributes.splice(indexOfAttributeTemplate + 1, 0, {
            id: METAATTRIBUTE_ID_FILTER_MENUS,
            mandatory: true,
            name: "Filter Menu",
            type: "select",
            value: null,
          });

          allAttributes.splice(indexOfAttributeTemplate + 2, 0, {
            id: METAATTRIBUTE_ID_FILTER_TEMPLATES,
            name: "Filter Template",
            type: "select",
            value: null,
          });

          allAttributes.splice(indexOfAttributeTemplate + 3, 0, {
            id: METAATTRIBUTE_ID_SOURCE_DATA,
            name: "Source Data",
            type: "select",
            value: null,
            mandatory: true,
          });

          allAttributes.splice(indexOfAttributeTemplate + 4, 0, {
            id: METAATTRIBUTE_ID_LIST1,
            name: "Name of List 1",
            type: "text",
            value: null,
            mandatory: true,
          });
          allAttributes.splice(indexOfAttributeTemplate + 5, 0, {
            id: METAATTRIBUTE_ID_LIST2,
            name: "Name of List 2",
            type: "text",
            value: null,
            mandatory: true,
          });
          allAttributes.splice(indexOfAttributeTemplate + 6, 0, {
            id: METAATTRIBUTE_ID_MULTIPLICITY_LIST1,
            name: "Multiplicity for List 1",
            type: "multiplicity",
            value: { text1: "", text2: "" },
            mandatory: true,
          });
          allAttributes.splice(indexOfAttributeTemplate + 7, 0, {
            id: METAATTRIBUTE_ID_MULTIPLICITY_LIST2,
            name: "Multiplicity for List 2",
            type: "multiplicity",
            value: { text1: "", text2: "" },
            mandatory: true,
          });
          allAttributes.splice(indexOfAttributeTemplate + 8, 0, {
            id: METAATTRIBUTE_ID_COMPOUND_VISUALIZATION,
            name: "Visualization",
            type: "select",
            value: null,
            mandatory: true,
          });
          setMandatoryAttributes([
            ...mandatoryAttributes,
            METAATTRIBUTE_ID_SOURCE_DATA,
            METAATTRIBUTE_ID_LIST1,
            METAATTRIBUTE_ID_LIST2,
            METAATTRIBUTE_ID_MULTIPLICITY_LIST1,
            METAATTRIBUTE_ID_MULTIPLICITY_LIST2,
          ]);
          break;
        }
        case "multipleSelect": {
          allAttributes.splice(indexOfAttributeTemplate + 1, 0, {
            id: METAATTRIBUTE_ID_DROPDOWNITEMS,
            mandatory: true,
            name: "Dropdown Items",
            type: "dropdownItems",
            value: null,
          });
          allAttributes.splice(indexOfAttributeTemplate + 2, 0, {
            id: METAATTRIBUTE_ID_MULTIPLICITY,
            mandatory: true,
            name: "Multiplicity",
            type: "multiplicity",
            value: { text1: "", text2: "" },
          });
          setMandatoryAttributes([
            ...mandatoryAttributes,
            METAATTRIBUTE_ID_DROPDOWNITEMS,
            METAATTRIBUTE_ID_MULTIPLICITY,
          ]);
          break;
        }
        case "select": {
          allAttributes.splice(indexOfAttributeTemplate + 1, 0, {
            id: METAATTRIBUTE_ID_DROPDOWNITEMS,
            mandatory: true,
            name: "Dropdown Items",
            type: "dropdownItems",
            value: null,
          });
          setMandatoryAttributes([
            ...mandatoryAttributes,
            METAATTRIBUTE_ID_DROPDOWNITEMS,
          ]);
          break;
        }
        case "relation": {
          allAttributes.splice(indexOfAttributeTemplate + 1, 0, {
            id: METAATTRIBUTE_ID_RELATION_HETEROGENIC,
            name: "Heterogenic",
            type: "switch",
            value: false,
          });

          allAttributes.splice(indexOfAttributeTemplate + 2, 0, {
            id: METAATTRIBUTE_ID_ALLOWEDLINKS,
            mandatory: true,
            name: "Related assets",
            type: "allowedLinks",
            value: null,
            multiplicity: "1..1",
          });

          allAttributes.splice(indexOfAttributeTemplate + 3, 0, {
            id: METAATTRIBUTE_ID_MULTIPLICITY,
            mandatory: true,
            name: "Multiplicity",
            type: "multiplicity",
            value: { text1: "", text2: "" },
          });
          allAttributes.splice(indexOfAttributeTemplate + 4, 0, {
            id: METAATTRIBUTE_ID_ATTRIBUTES_LOOKUP,
            mandatory: false,
            name: "Displayed Attribute",
            type: "composite",
            value: null,
            multiplicity: "0..1",
            disabled: true,
          });

          setMandatoryAttributes([
            ...mandatoryAttributes,
            METAATTRIBUTE_ID_MULTIPLICITY,
            METAATTRIBUTE_ID_ALLOWEDLINKS,
            METAATTRIBUTE_ID_RELATION_VISUALIZATION,
          ]);
          break;
        }
        case "regex": {
          allAttributes.splice(indexOfAttributeTemplate + 1, 0, {
            id: METAATTRIBUTE_ID_REGEX,
            mandatory: false,
            name: "Regex",
            type: "regex",
            value: null,
          });
          break;
        }
        case "lifecycle": {
          allAttributes.splice(indexOfAttributeTemplate + 1, 0, {
            id: METAATTRIBUTE_ID_SOURCE_DATA,
            mandatory: true,
            name: "Source Data",
            type: "select",
            value: null,
          });
          setMandatoryAttributes([
            ...mandatoryAttributes,
            METAATTRIBUTE_ID_SOURCE_DATA,
          ]);
        }
      }

      setAttributes(allAttributes);
    };

    const handleEdit = (id, value, name, attrType) => {
      if (["password"].includes(attrType) && setSpecialAttribute) {
        setSpecialAttribute(true);
      }

      if ([METAATTRIBUTE_ID_PARENT1, METAATTRIBUTE_ID_PARENT2].includes(id)) {
        setDebouncedParentId([
          value,
          id === METAATTRIBUTE_ID_PARENT1 ? "parent1" : "parent2",
        ]);
      }

      if (id === METAATTRIBUTE_ID_ATRIBUTETYPE) {
        generateMetamodelAttributes(value);
        dispatch(setWorkingVersionMask(true));
      } else {
        let allAttributes = [...attributes];

        if (id === METAATTRIBUTE_ID_ALLOWEDLINKS) {
          const attributeLookup = allAttributes?.find(
            (attr) => attr.id === METAATTRIBUTE_ID_ATTRIBUTES_LOOKUP
          );
          if (attributeLookup) {
            attributeLookup["disabled"] = value?.length === 0;
          }
        }
        if (id === METAATTRIBUTE_ID_RELATION_HETEROGENIC) {
          const relatedAssets = allAttributes?.find(
            (attr) => attr.id === METAATTRIBUTE_ID_ALLOWEDLINKS
          );
          relatedAssets["value"] = value
            ? relatedAssets?.value
            : relatedAssets?.value
            ? relatedAssets?.value?.splice(0, 1)
            : relatedAssets?.value;
          relatedAssets["multiplicity"] = value ? "1..n" : "1..1";
          if (value) {
            allAttributes = allAttributes?.filter(
              (attr) => attr.id !== METAATTRIBUTE_ID_ATTRIBUTES_LOOKUP
            );
          } else {
            allAttributes.splice(4, 0, {
              id: METAATTRIBUTE_ID_ATTRIBUTES_LOOKUP,
              mandatory: false,
              name: "Displayed Attribute",
              type: "composite",
              value: null,
              disabled: relatedAssets?.value?.length === 0,
              multiplicity: "0..1",
            });
          }
        }
        if (id === METAATTRIBUTE_ID_ALLOWEDLINKS) {
          const attributeLookupIndex = allAttributes?.findIndex(
            (attr) => attr.id === METAATTRIBUTE_ID_ATTRIBUTES_LOOKUP
          );
          if (attributeLookupIndex !== -1)
            allAttributes[attributeLookupIndex]["value"] = null;
        }

        if (name === "dropdownItems") {
          let valueIndex;
          // for single select
          valueIndex = allAttributes.findIndex(
            (item) => item.type === "select"
          );
          const findIndex = value?.find(
            (item) => item.value === allAttributes[valueIndex]?.value
          );
          if (!findIndex || findIndex === -1) {
            allAttributes[valueIndex].value = "";
          }

          // for multi-select

          valueIndex = allAttributes.findIndex(
            (item) => item.type === "multipleSelect"
          );
          const multipleItems = [];
          allAttributes[valueIndex]?.value?.forEach((multiSelect) => {
            const findIndex = value?.find((item) => item.value === multiSelect);
            if (findIndex) {
              multipleItems.push(multiSelect);
            }
          });
          allAttributes[valueIndex].value = multipleItems;
        }

        let findIndex = allAttributes.findIndex((item) => item.id === id);
        allAttributes[findIndex].value = value;

        if (id === METAATTRIBUTE_ID_PARENT1) {
          findIndex = allAttributes.findIndex(
            (item) => item.id === METAATTRIBUTE_ID_COMPOUND_TEMPLATE1
          );
          allAttributes[findIndex].value = null;
        }

        if (id === METAATTRIBUTE_ID_PARENT2) {
          findIndex = allAttributes.findIndex(
            (item) => item.id === METAATTRIBUTE_ID_COMPOUND_TEMPLATE2
          );
          allAttributes[findIndex].value = null;
        }

        if (id === METAATTRIBUTE_ID_FILTER_MENUS) {
          findIndex = allAttributes.findIndex(
            (item) => item.id === METAATTRIBUTE_ID_FILTER_TEMPLATES
          );
          allAttributes[findIndex].value = null;
          findIndex = allAttributes.findIndex(
            (item) => item.id === METAATTRIBUTE_ID_SOURCE_DATA
          );
          allAttributes[findIndex].value = null;
        }

        setAttributes([...allAttributes]);

        const hasChanges = allAttributes?.some((attr, index) => {
          return !isEqual(attr.value, initialAttributes[index]?.value);
        });
        dispatch(setWorkingVersionMask(hasChanges));
        if (name === "allowedChildren") {
          updateAllowedChildrens && updateAllowedChildrens(value);
        }
      }
    };

    const generateTemplates = () => {
      const templates = queryClient.getQueryData(
        GET_ALL_TEMPLATES_KEY
      ) as ITemplates[];

      const templateOptions = [];
      templates?.forEach((temp) => {
        if (temp.nodeType === "DATA")
          templateOptions.push({
            value: temp?.id.toString(),
            label: temp?.name,
          });
      });
      return templateOptions;
    };

    const generateFilterMenus = () => {
      const allHeaderData = queryClient.getQueryData(GET_HEADER_MENUS) as any;
      const filterMenus = [];
      allHeaderData?.menu?.forEach((item) => {
        filterMenus.push({
          value: item?.id.toString(),
          label: item?.name,
        });
      });
      return filterMenus;
    };

    const generateFilterTemplates = (menuId) => {
      const allHeaderData = queryClient.getQueryData(GET_HEADER_MENUS) as any;
      const selectedMenu = allHeaderData?.menu?.find(
        (menu) => menu.id == menuId
      );

      const filterMenus = [];
      selectedMenu?.allowedChildren?.forEach((allowedChild) => {
        filterMenus.push({
          value: allowedChild.id.toString(),
          label: allowedChild?.name,
        });
      });
      return filterMenus;
    };

    const generateAllowedChildrens = (parentId) => {
      const nodeDetails = queryClient.getQueryData([
        GET_NODE_ATTRIBUTES_DETAILS,
        parentId?.toString(),
      ]) as INodeDetails;
      // if (!nodeDetails) {
      //   nodeDetails = (await getNodeDetails(
      //     parentId?.toString()
      //   )) as INodeDetails;
      // }
      if (nodeDetails) {
        if (nodeDetails?.templateId === TEMP_GROUPING_NODE_ID) {
          const allowedChildrens = nodeDetails?.body?.find(
            (attr) => attr.type === "allowedChildren"
          );
          if (allowedChildrens) {
            return allowedChildrens?.value?.map((allowedChild) => {
              return {
                value: allowedChild?.id.toString(),
                label: allowedChild?.name,
              };
            });
          }
        }
        const selectedTemplate = templatesData[nodeDetails?.templateId];

        if (selectedTemplate?.allowedChildren) {
          return selectedTemplate?.allowedChildren?.map((allowedChild) => {
            return {
              value: allowedChild?.id.toString(),
              label: allowedChild?.name,
            };
          });
        }
      }
    };

    const getSelectItems = (id, items, type) => {
      if (id === METAATTRIBUTE_ID_COMPOUND_VISUALIZATION) {
        return COMPOUND_VISUALIZATION;
      }

      if (id === METAATTRIBUTE_ID_COMPOUND_VARIANT) {
        return COMPOUND_VARIANT;
      }
      if (id === METAATTRIBUTE_ID_COMPOUND_TEMPLATE1) {
        const parentId = attributes?.find(
          (attr) => attr.id === METAATTRIBUTE_ID_PARENT1
        )?.value;
        if (parentId) {
          return generateAllowedChildrens(parentId);
        }

        return generateTemplates();
      }

      if (id === METAATTRIBUTE_ID_COMPOUND_TEMPLATE2) {
        const parentId = attributes?.find(
          (attr) => attr.id === METAATTRIBUTE_ID_PARENT2
        )?.value;

        if (parentId) {
          return generateAllowedChildrens(parentId);
        }
        return generateTemplates();
      }
      if (id === METAATTRIBUTE_ID_FILTER_MENUS) {
        return generateFilterMenus();
      }

      if (id === METAATTRIBUTE_ID_FILTER_TEMPLATES) {
        const selectedMenu = Object.keys(
          attributes?.find((attr) => attr.id === METAATTRIBUTE_ID_FILTER_MENUS)
            ?.value || {}
        );
        if (selectedMenu?.length > 0) {
          return generateFilterTemplates(selectedMenu[0]);
        }
      }

      if (type === "relation") {
        return (
          attributes?.find((attr) => attr.type === "allowedChildren")?.value ||
          []
        );
      }

      if (id === METAATTRIBUTE_ID_DISPLAY_OPTIONS) {
        return [
          { value: "hierarchy", label: t("Hierarchy") },
          { value: "tabular", label: t("Tabular") },
        ];
      }

      if (id === METAATTRIBUTE_ID_ATRIBUTETYPE) {
        if (templatesData) {
          const attributeTemplate =
            templatesData[TEMPLATES_ATTRIBUTE_TEMPLATE_ID]
              ?.attributeTemplates || [];
          if (attributeTemplate && attributeTemplate.length > 0) {
            const items = attributeTemplate.find(
              (item) => item.id === METAATTRIBUTE_ID_ATRIBUTETYPE
            )?.items;
            if (items && Object.keys(items).length > 0) {
              const dropdownItems = [];
              Object.keys(items).map((item) => {
                dropdownItems.push({ label: items[item], value: item });
              });

              return dropdownItems;
            }
          }
        }
      } else if (items) {
        const dropdownItems = [];
        Object.keys(items).map((item) => {
          dropdownItems.push({ label: items[item], value: item });
        });
        return dropdownItems;
      }
      return [];
    };

    const escFunction = useCallback((event) => {
      if (event.key === "Escape") {
        setEditingAttribute(null);
      }
    }, []);

    useEffect(() => {
      document.addEventListener("keydown", escFunction, false);
      return () => {
        document.removeEventListener("keydown", escFunction, false);
      };
    }, [escFunction]);

    const getAllowedChildrens = () => {
      const attributeType = attributes?.find((attr) => attr.id === -150)?.value;
      if (attributeType) {
        const type = Object.keys(attributeType)[0];
        if (type === "relation") {
          return attributes?.find((attr) => attr.type === "allowedLinks")
            ?.value;
        }
      }
      return null;
    };

    const allowedChildrens = getAllowedChildrens();

    const [sourceData, setSourceData] = useState([]);

    useEffect(() => {
      const selectedAttribute =
        attributes?.find((attr) => attr.id === METAATTRIBUTE_ID_ATRIBUTETYPE)
          ?.value || {};

      if (
        Object.keys(selectedAttribute)?.length > 0 &&
        Object.keys(selectedAttribute)[0] === "lifecycle"
      ) {
        getSourceForLifecycle().then((response: any) => {
          const sourceData = [];
          response?.forEach((data) => {
            sourceData.push({
              value: data?.id?.toString(),
              label: data?.name,
            });
          });
          setSourceData(sourceData);
        });
      }
    }, [attributes]);

    useEffect(() => {
      const selectedAttribute =
        attributes?.find((attr) => attr.id === METAATTRIBUTE_ID_ATRIBUTETYPE)
          ?.value || {};
      if (
        Object.keys(selectedAttribute)?.length > 0 &&
        (Object.keys(selectedAttribute)[0] === "compoundSimple" ||
          Object.keys(selectedAttribute)[0] === "roleMatrix")
      ) {
        const dataSources = [];
        setSourceData(dataSources);

        const selectedMenu = Object.keys(
          attributes?.find((attr) => attr.id === METAATTRIBUTE_ID_FILTER_MENUS)
            ?.value || {}
        )[0];

        const selectedTemplate = Object.keys(
          attributes?.find(
            (attr) => attr.id === METAATTRIBUTE_ID_FILTER_TEMPLATES
          )?.value || {}
        )[0];

        if (selectedMenu) {
          API.get<any, any>(
            `/nodes/${selectedMenu}/alllevelschildren${
              selectedTemplate ? `?templateId=${selectedTemplate}` : ""
            }`
          ).then((response) => {
            const sourceData = [];
            response?.forEach((data) => {
              sourceData.push({
                value: data?.id?.toString(),
                label: data?.name,
              });
            });
            setSourceData(sourceData);
          });
        }
      }
    }, [attributes]);

    const selectedAttributeType = useMemo(() => {
      const selectedAttribute = attributes?.find(
        (attr) => attr.id === METAATTRIBUTE_ID_ATRIBUTETYPE
      )?.value;
      return selectedAttribute ? Object.keys(selectedAttribute)[0] : null;
    }, [attributes]);

    const checkDisabled = (id, disabled) => {
      if (selectedAttributeType === "compound") {
        const selectedVariant = attributes?.find(
          (attr) => attr.id === METAATTRIBUTE_ID_COMPOUND_VARIANT
        )?.value;

        if (selectedVariant) {
          const variant = Object.keys(selectedVariant)[0];
          if (variant === "COMPOUND_HIERARCHY") {
            return [
              METAATTRIBUTE_ID_PARENT2,
              METAATTRIBUTE_ID_PARENT2_PREVIEW,
              METAATTRIBUTE_ID_COMPOUND_TEMPLATE2,
            ].includes(id);
          }
          return false;
        }

        return [
          METAATTRIBUTE_ID_PARENT1,
          METAATTRIBUTE_ID_PARENT2,
          METAATTRIBUTE_ID_LIST1,
          METAATTRIBUTE_ID_LIST2,
          METAATTRIBUTE_ID_MULTIPLICITY_LIST1,
          METAATTRIBUTE_ID_MULTIPLICITY_LIST2,
          METAATTRIBUTE_ID_COMPOUND_VISUALIZATION,
          METAATTRIBUTE_ID_PARENT1_PREVIEW,
          METAATTRIBUTE_ID_PARENT2_PREVIEW,
          METAATTRIBUTE_ID_COMPOUND_TEMPLATE1,
          METAATTRIBUTE_ID_COMPOUND_TEMPLATE2,
        ].includes(id);
      }
      return disabled;
    };

    return (
      <Wrapper
        id="working-version-area"
        onClick={(e) => e.stopPropagation()}
        style={{ border: workingVersionActive ? "1px solid red" : "none" }}
      >
        {loading || isLoading ? (
          <SkeletonWrapper>
            <div className="loader" key={`skeleton`}>
              <LoadingOutlined />
            </div>
          </SkeletonWrapper>
        ) : attributes.length === 0 && newAttributes.length === 0 ? (
          <EmptyContainer>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={t("No attributes")}
            />
          </EmptyContainer>
        ) : isError ? (
          <EmptyContainer>
            <div style={{ marginBottom: 16 }}>
              <span style={{ color: "#d4380d", fontWeight: 600 }}>
                {error?.data?.details ||
                  (error?.status === 409
                    ? "Invalid node id!"
                    : "Error Occurred!")}
              </span>
            </div>
          </EmptyContainer>
        ) : (
          <div className="attributes-wrapper">
            {attributes.map((item) => {
              return (
                <AttributeItem
                  workingVersion
                  readOnly={!permissions.includes("EDIT")}
                  key={item.id}
                  {...item}
                  onEdit={(value) =>
                    handleEdit(item.id, value, item.name, item.type)
                  }
                  title={item.name}
                  dropdownItems={
                    item?.id === METAATTRIBUTE_ID_SOURCE_DATA
                      ? sourceData
                      : getSelectItems(item?.id, item?.items, item?.type)
                  }
                  allowedChildren={allowedChildrens}
                  isEditing={editingAttribute === item.id}
                  onEditClick={setEditingAttribute}
                  titleClassName={"attribute-title"}
                  disabled={checkDisabled(item.id, item?.disabled)}
                />
              );
            })}
          </div>
        )}
      </Wrapper>
    );
  }
);

export { WorkingVersionArea };

const EmptyContainer = styled.div`
  margin-top: 15%;
`;

const SkeletonWrapper = styled.div`
  & .loader {
    display: flex;
    min-height: 300px;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    color: #4277a2;
  }
`;
const Wrapper = styled.div`
  padding: 10px;

  flex: 1;
  overflow: auto;
  min-width: 200px;
`;
