import React, { Suspense, useEffect } from "react";
import { Route, Routes } from "react-router-dom";
import "./App.css";
import { Layout } from "./components";
import { PrivateRoute } from "./utils/PrivateRoute";
import { RestrictedRoute } from "./utils/RestrictedRoute";
import { LoadingOutlined } from "@ant-design/icons";
import { QueryClient, QueryClientProvider } from "react-query";
import { ReactQueryDevtools } from "react-query/devtools";
import { Flex } from "antd";
import { useDispatch } from "react-redux";
import { setMenuCollapsed } from "./store/features/sidebar";
import { setTrashCollapsed } from "./store/features/trashcan";
import { AdminRoute } from "./utils/AdminRoute";
import { UserRoute } from "./utils/UserRoute";
import { ErrorBoundary } from "./components/ErrorBoundary";

const LoginPage = React.lazy(() => import("./pages/Login"));
const HomePage = React.lazy(() => import("./pages/MainHome"));
const TranslationsPage = React.lazy(() => import("./pages/Translations"));
const PageNotFound = React.lazy(() => import("./pages/404"));
const UnderConstruction = React.lazy(() => import("./pages/UnderConstruction"));
const DetailsPage = React.lazy(() => import("./pages/Details"));
const MessagePage = React.lazy(() => import("./pages/Messages"));
const SettingsPage = React.lazy(() => import("./pages/Settings"));
const ChangeThemePage = React.lazy(() => import("./pages/ChangeTheme"));
const GraphPage = React.lazy(() => import("./pages/Graph"));
const HistoryPage = React.lazy(() => import("./pages/HistoryPage"));
const FavoritesPage = React.lazy(() => import("./pages/Favorites"));
const MyCommentsPage = React.lazy(() => import("./pages/MyComments"));
const GeneralSettings = React.lazy(
  () => import("./pages/Settings/GeneralSettings")
);
const MenuCreatorPage = React.lazy(() => import("./pages/MenuCreator"));
const MetamodelSettingsPage = React.lazy(
  () => import("./pages/Settings/MetamodelSettings")
);
const Users2Settings = React.lazy(
  () => import("./pages/Settings/UsersPermission")
);
const ResetDatabase = React.lazy(() => import("./pages/ResetDatabase"));
const ApplicationPropertiesPage = React.lazy(
  () => import("./pages/Settings/AppProperties")
);
const BusinessLogs = React.lazy(() => import("./pages/BusinessLogs"));
const TechnicalLogs = React.lazy(() => import("./pages/TechnicalLogs"));
const Profile = React.lazy(() => import("./pages/Profile"));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      cacheTime: Infinity,
    },
  },
});

const FullPageSpinner = () => (
  <Flex align="center" justify="center" style={{ height: "100vh" }}>
    <LoadingOutlined style={{ fontSize: "30px", color: "#4377a2" }} />
  </Flex>
);

function App() {
  const dispatch = useDispatch();

  useEffect(() => {
    const isCollapsed = localStorage.getItem("menu-collapsed");
    if (isCollapsed) {
      dispatch(setMenuCollapsed(isCollapsed === "1"));
    }

    const isTrashCollapsed = localStorage.getItem("trash-collapsed");
    if (isTrashCollapsed) {
      dispatch(setTrashCollapsed(isTrashCollapsed !== "1"));
    }
  }, [dispatch]);

  return (
    <QueryClientProvider client={queryClient}>
      <ReactQueryDevtools initialIsOpen={false} />
      <ErrorBoundary>
        <Routes>
          <Route element={<Layout />}>
            <Route path="/roles" element={<UnderConstruction />} />
            <Route path="/user-groups" element={<UnderConstruction />} />
            <Route path="/users" element={<UnderConstruction />} />
            <Route path="/schedules" element={<UnderConstruction />} />
            <Route
              path="/settings/profile"
              element={
                <PrivateRoute>
                  <Profile />
                </PrivateRoute>
              }
            />
            <Route path="/reset-database" element={<ResetDatabase />} />
            <Route path="*" element={<PageNotFound />} />
            <Route
              path="/"
              element={
                <PrivateRoute>
                  <HomePage />
                </PrivateRoute>
              }
            />
            <Route
              path="/settings/translations"
              element={
                <AdminRoute>
                  <TranslationsPage />
                </AdminRoute>
              }
            />
            <Route
              path="/settings/app-properties"
              element={
                <AdminRoute>
                  <ApplicationPropertiesPage />
                </AdminRoute>
              }
            />
            <Route
              path="/details/graph/:nodeId"
              element={
                <UserRoute>
                  <GraphPage />
                </UserRoute>
              }
            />
            <Route
              path="/business-log"
              element={
                <AdminRoute>
                  <BusinessLogs />
                </AdminRoute>
              }
            />
            <Route
              path="/technical-log"
              element={
                <AdminRoute>
                  <TechnicalLogs />
                </AdminRoute>
              }
            />
            <Route
              path="/history"
              element={
                <UserRoute>
                  <HistoryPage />
                </UserRoute>
              }
            />
            <Route
              path="/message"
              element={
                <UserRoute>
                  <MessagePage />
                </UserRoute>
              }
            />
            <Route
              path="/comments"
              element={
                <UserRoute>
                  <MyCommentsPage />
                </UserRoute>
              }
            />
            <Route
              path="/pinned"
              element={
                <UserRoute>
                  <FavoritesPage />
                </UserRoute>
              }
            />
            <Route
              path="/settings"
              element={
                <PrivateRoute>
                  <SettingsPage />
                </PrivateRoute>
              }
            />
            <Route
              path="/settings/users/:nodeId"
              element={
                <PrivateRoute>
                  <Users2Settings />
                </PrivateRoute>
              }
            />
            <Route
              path="/settings/general"
              element={
                <AdminRoute>
                  <GeneralSettings />
                </AdminRoute>
              }
            />
            <Route
              path="/settings/data-sources/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />
            {/* <Route path="/settings/users/:nodeId" element={<AdminRoute><MetamodelSettingsPage /></AdminRoute>} /> */}
            <Route
              path="/settings/user-groups/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />
            <Route
              path="/settings/roles/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />
            <Route
              path="/settings/actions/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />
            <Route
              path="/settings/metamodel/:nodeId"
              element={
                <AdminRoute>
                  <MetamodelSettingsPage />
                </AdminRoute>
              }
            />
            <Route
              path="/settings/menu-creator"
              element={
                <AdminRoute>
                  <MenuCreatorPage />
                </AdminRoute>
              }
            />
            <Route
              path="/settings/theme"
              element={
                <UserRoute>
                  <ChangeThemePage />
                </UserRoute>
              }
            />
            <Route
              path="/details/:nodeId"
              element={
                <UserRoute>
                  <DetailsPage />
                </UserRoute>
              }
            />
          </Route>
          <Route
            path="/login"
            element={
              <RestrictedRoute>
                <Suspense fallback={<FullPageSpinner />}>
                  <LoginPage />
                </Suspense>
              </RestrictedRoute>
            }
          />
        </Routes>
      </ErrorBoundary>
    </QueryClientProvider>
  );
}

export default App;
