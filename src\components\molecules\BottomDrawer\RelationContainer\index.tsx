import { styled } from "@linaria/react";
import { Dropdown } from "antd";
import { useEffect, useRef, useState } from "react";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
  GET_LOCAL_SETTINGS_KEY,
  GET_RELATIONS,
  NODES_MENU_ITEMS,
  TRASH_NODES_MENU_ITEMS,
  getAttributeIcon,
} from "../../../../constants";
import { DetailsContainer, IAGColumns, MyTable } from "../../../organisms";
import { getParentID, transformObjectPath } from "../../../../utils";
import { useSearchParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { ILocalSettings, IRelations } from "../../../../interfaces";
import { getNodeRelations, saveLocalSettings } from "../../../../services";
import { setBottomDrawerMask } from "../../../../store/features";
import {
  useHyperlinkActions,
  useNotification,
} from "../../../../utils/functions/customHooks";
import { setTrashcanDrawerMask } from "../../../../store/features/trashcan";
import { RootState } from "../../../../store";
import { debounce } from "lodash";

// Bottom drawer for displaying relations of a node
const RelationContainer = ({ id, fromTrashcan, displaySaveCancel }) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();

  const parentRef = useRef(null);

  const [height, setHeight] = useState(0);
  const [isDetailsOpen, setDetailsOpen] = useState(null);

  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const isPathNameHidden = useSelector(
    (state: RootState) => state.breadcrumbs.hideAllpathNames
  );

  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();
  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const COLUMNS = [
    {
      headerName: "Relation",
      field: "relationName",
      minWidth: 200,
      flex: 1,
    },
    {
      headerName: "Name",
      field: "nodeName",
      width: 170,
      cellRenderer: (params) => {
        const record = params?.data as IRelations;
        return (
          <Dropdown
            menu={{
              items: record.inTrash ? TRASH_NODES_MENU_ITEMS : NODES_MENU_ITEMS,
              onClick: (e) =>
                handleNodeClick(e.key, record.nodeId, record.nodeName),
            }}
            trigger={["contextMenu"]}
          >
            <a
              className="title-container"
              onClick={async (e) => {
                e.stopPropagation();
                handleHyperlinkAction({
                  id: record.nodeId,
                  inTrash: record.inTrash,
                });
              }}
            >
              <p className={` ${record.inTrash ? "trash-hyperlink" : ""}`}>
                {record.nodeName}
              </p>
            </a>
          </Dropdown>
        );
      },
    },
    {
      headerName: "Template",
      field: "nodeTemplateName",
      width: 200,
      cellRenderer: (params) => {
        const record = params?.data as IRelations;
        const templateIcon =
          templatesData[Number(record?.nodeTemplateId)]?.icon || "_30_folder";

        return (
          <p className="title-container">
            {getAttributeIcon(templateIcon)}
            {record.nodeTemplateName}
          </p>
        );
      },
    },

    {
      headerName: "Path",
      field: "nodePath",
      width: 340,
      cellRenderer: ({ data }) => (
        <p className="right-align">
          {isPathNameHidden
            ? ""
            : data?.nodePath
            ? transformObjectPath(data?.nodePath, data.inTrash)
            : "-"}
        </p>
      ),
    },
  ] as IAGColumns[];

  const [columns, setColumns] = useState(COLUMNS);

  // fetching relations
  const { data, isLoading, isError } = useQuery(
    [GET_RELATIONS, id],
    () => getNodeRelations(id),
    {
      enabled: !!id && !searchParams.get("draft"),
    }
  );

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const baseUrl =
          import.meta.env.VITE_APP_BASE_URL === "/"
            ? ""
            : import.meta.env.VITE_APP_BASE_URL;

        const parentID = await getParentID(id);

        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
        return;
      }

      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  const detectChange = () => {
    if (fromTrashcan) {
      dispatch(setTrashcanDrawerMask(true));
    } else {
      dispatch(setBottomDrawerMask(true));
    }
  };

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }
    if (
      localSettingsData &&
      localSettingsData?.body[0]?.value?.relationsDrawer &&
      localSettingsData?.body[0]?.value?.relationsDrawer?.columns
    ) {
      const pinned =
        localSettingsData?.body[0]?.value?.relationsDrawer?.pinned || [];
      const sort =
        localSettingsData?.body[0]?.value?.relationsDrawer?.sort || [];

      const allColumns = [];
      localSettingsData.body[0].value.relationsDrawer.columns?.forEach(
        (column) => {
          const index = COLUMNS.findIndex((item) => item.field == column);
          if (column)
            allColumns.push({
              ...COLUMNS[index],
              pinned: pinned?.includes(column) ? "left" : null,
              sort: sort?.find((val) => val.colId === column)?.sort || null,
            });
        }
      );

      setColumns(allColumns);
    } else {
      setColumns(COLUMNS);
    }
  }, [localSettingsData, isPathNameHidden]);

  const handleCancel = () => {
    setTimeout(() => {
      if (fromTrashcan) {
        dispatch(setTrashcanDrawerMask(false));
      } else {
        dispatch(setBottomDrawerMask(false));
      }
    }, 200);
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setBottomDrawerMask(false));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setBottomDrawerMask(false));
    },
  });

  const handleSave = (newColumns: string[], filters, sort, pinned) => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        relationsDrawer: {
          columns: newColumns,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  useEffect(() => {
    const parent = parentRef.current;
    if (!parent) return;

    const updateHeight = debounce((entries) => {
      for (const entry of entries) {
        setHeight(entry.contentRect.height);
      }
    }, 100);

    const observer = new ResizeObserver(updateHeight);
    observer.observe(parent);

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <Wrapper
      ref={parentRef}
      style={{ border: displaySaveCancel ? "1px solid red" : "none" }}
    >
      {contextHolder}

      <MyTable
        isError={isError}
        emptyMessage="No relations"
        loading={isLoading}
        height={`${height - 50}px`}
        detectChange={detectChange}
        onCancelClick={handleCancel}
        saveLoading={mutation.isLoading}
        onSaveClick={handleSave}
        data={data?.map((item, index) => ({
          ...item,
          id: index + 1,
        }))}
        columns={columns}
        excelFileName="relations"
        initialFilters={
          localSettingsData?.body[0]?.value?.relationsDrawer?.filters || {}
        }
        displaySaveCancel={displaySaveCancel}
      />

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </Wrapper>
  );
};
export { RelationContainer };

const Wrapper = styled.div`
  height: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  padding: 10px;

  & .p-datatable {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  & .data-table-wrapper {
    overflow: hidden;
    height: 100%;
  }

  & .p-datatable-wrapper {
    flex: 1;
  }
  & .p-datatable-header {
    overflow-x: auto;
    overflow-y: hidden;
  }

  & a:hover {
    text-decoration: underline;
  }
  & td {
    cursor: default !important;
  }

  & .title-container {
    display: flex;
    align-items: center;
    gap: 6px;
    text-align: left;

    & img {
      width: 18px;
      height: 18px;
      object-fit: contain;
    }
  }
  & a:hover {
    color: #094f8b;
  }
`;
