import { styled } from "@linaria/react";
import { Editor } from "@tinymce/tinymce-react";
import { Button, Input } from "antd";
import i18next from "i18next";
import { Dialog } from "primereact/dialog";
import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";

interface Props {
  value: any;
  onEditorChange?: any;
  minimal?: boolean;
  noFocus?: boolean;
  disabled?: boolean;
}

type IframeType = {
  url: string;
  height: string;
  width: string;
};
const TinyEditor = ({
  value,
  onEditorChange,
  minimal,
  noFocus,
  disabled,
}: Props) => {
  const editorRef = useRef(null);
  const { t } = useTranslation();

  const [iframe, setIframe] = useState(null as IframeType);
  const [showIframeModal, setShowIframeModal] = useState(false);
  const editorProperties = useSelector(
    (state: RootState) => state.globalSettings.editorProperties
  );

  const handleEditorInit = (_, editor) => {
    if (editor && !noFocus) {
      editorRef.current = editor;
      editorRef.current.focus();
    }
  };

  // Temporary API key as we are going to replace the editor with a new one
  const API = "inhu1lylyd1dgf5tz1ds2yb0gxbm1ntp2j3cspw9d5e2if3i";

  const getMenuBar = () => {
    return editorProperties?.menubar;
  };

  const getPlugins = () => {
    return minimal
      ? "preview charmap emoticons wordcount"
      : "preview charmap emoticons image link lists searchreplace table wordcount";
  };

  const getToolbar = () => {
    return minimal
      ? "undo redo | fontfamily fontsize align | bold italic underline strikethrough | emoticons"
      : editorProperties?.toolbar;
  };

  const generateURL = () => {
    if (!iframe?.url.startsWith("http")) {
      return `https://${iframe?.url}`;
    }

    return iframe?.url;
  };

  const insertIframe = () => {
    const iframeHTML = `<iframe src="${generateURL()}" width=${
      iframe?.width
    } height=${iframe?.height} frameborder="0" allowfullscreen></iframe>`;
    editorRef.current.insertContent(iframeHTML);
    setShowIframeModal(false);
  };

  return (
    <>
      <Editor
        apiKey={API}
        init={{
          language: i18next.language.startsWith("pl") ? "pl" : "en",
          setup: (editor) => {
            editor.on("init", (evt) => handleEditorInit(evt, editor));
            editor.ui.registry.addButton("insertIframe", {
              text: "Iframe",
              onAction: () => setShowIframeModal(true),
            });
          },
          branding: false,

          menubar: minimal ? false : getMenuBar(),
          plugins: getPlugins(),
          toolbar: getToolbar(),
          elementpath: false,
          extended_valid_elements:
            "iframe[src|frameborder|style|scrolling|class|width|height|name|align]",
          content_style:
            "tbody, tr, td {border-style: inherit !important;}  p { margin: 0 }",
        }}
        value={value}
        onEditorChange={onEditorChange}
        disabled={disabled}
      />

      <Dialog
        visible={showIframeModal}
        onHide={() => {
          setShowIframeModal(false);
        }}
        footer={null}
        className="export-modal draggable-modal"
        header={"Iframe"}
      >
        <Iframe>
          <div className="row">
            <label>URL</label>
            <Input
              onChange={(e) => {
                setIframe({ ...iframe, url: e.target.value });
              }}
              value={iframe?.url || ""}
            />
          </div>

          <div className="row">
            <label>{t("Width")}</label>
            <Input
              onChange={(e) => {
                setIframe({ ...iframe, width: e.target.value });
              }}
              value={iframe?.width || ""}
            />
          </div>

          <div className="row">
            <label>{t("Height")}</label>
            <Input
              onChange={(e) => {
                setIframe({ ...iframe, height: e.target.value });
              }}
              value={iframe?.height || ""}
            />
          </div>
          <div className="buttons">
            <Button
              type="primary"
              disabled={!iframe?.url}
              onClick={insertIframe}
            >
              {t("Save")}
            </Button>
          </div>
        </Iframe>
      </Dialog>
    </>
  );
};

export { TinyEditor };

const Iframe = styled.div`
  padding-bottom: 8px;

  & .row {
    margin-bottom: 8px;
  }
  & label {
    margin-bottom: 2px;
    display: block;
  }
  & .buttons {
    display: flex;
    justify-content: flex-end;
  }
`;
