{"Login": "<PERSON><PERSON>", "Password": "Password", "History": "History", "User": "User", "Attribute": "Attribute", "Old Value": "Old Value", "New Value": "New Value", "Settings": "Settings", "Home": "Home", "Personal profile": "Personal profile", "Favourites": "Favourites", "Messages": "Messages", "My Grades": "My Grades", "Business Log": "Business Log", "Technical Log": "Technical Log", "Data Sources": "Data Sources", "Users": "Users", "Roles": "Roles", "General Settings": "General Settings", "Translations": "Translations", "Theme & Layout": "Theme & Layout", "Menu Creator": "<PERSON><PERSON>", "DQM": "DQM", "Metamodel": "Metamodel", "Search": "Search", "Message": "Message", "Name": "Name", "Drag to create new group": "<PERSON>ag to create new group", "Add group": "Add group", "Rename": "<PERSON><PERSON>", "Update": "Update", "Add": "Add", "Open in new tab": "Open in new tab", "Restart Graph in a new tab": "<PERSON><PERSON> Graph in a new tab", "My Comments": "My Comments", "Add new item": "Add new item", "Upload": "Upload", "Unable to move to node!": "Unable to move to node!", "Node may be deleted!": "Node may be deleted!", "COMMENTS": "COMMENTS", "MESSAGES": "MESSAGES", "HISTORY": "HISTORY", "Asset Name": "Asset Name", "Date": "Date", "Author": "Author", "Commented By": "Commented By", "Sort Ascending": "Sort Ascending", "Sort Descending": "Sort Descending", "Remove Sorting": "Remove Sorting", "Pin": "<PERSON>n", "Pinned": "Pinned", "Comments": "Comments", "Description": "Description", "Filters": "Filters", "All": "All", "Rating": "Rating", "Version": "Version", "Last Viewed": "Last Viewed", "Sorry, the page you visited does not exist.": "Sorry, the page you visited does not exist.", "Back Home": "Back Home", "Logo": "Logo", "Edit": "Edit", "Application Title": "Application Title", "Display Logo": "Display Logo", "Display Application Name": "Display Application Name", "Menu updated successfully!": "Menu updated successfully!", "Error in saving menus!": "Error in saving menus!", "Save": "Save", "Click to add new menu": "Click to add new menu", "Add New Menu": "Add New Menu", "No menus created yet!": "No menus created yet!", "Add asset type": "Add asset type", "Add menu": "Add menu", "Delete selected": "Delete selected", "Cut": "Cut", "Print": "Print", "Delete": "Delete", "Share": "Share", "Export": "Export", "No attributes": "No attributes", "New attributes exists": "New attributes exists", "useful": "{{count}} useful", "not useful": "{{count}} not useful", "Relationship": "Relationship", "Charts": "Charts", "Attachments": "Attachments", "Graph": "Graph", "Attachment": "Attachment", "Preview": "Preview", "Uploaded By": "Uploaded By", "Type": "Type", "Relation": "Relation", "Template": "Template", "Path": "Path", "Name not unique!": "Name not unique!", "Error Occurred": "<PERSON><PERSON><PERSON>urred", "Add New Language": "Add New Language", "Publish": "Publish", "Error Occurred!": "Error Occurred!", "Error in saving translations!": "Error in saving translations!", "Success!": "Success!", "Translations Published Successfully!": "Translations Published Successfully!", "Reset to Default": "Reset to De<PERSON>ult", "Enter text here...": "Enter text here...", "Left": "Left", "Center": "Center", "Footer Text": "Footer Text", "Right": "Right", "Choose alignment": "Choose alignment", "Current version": "Current version", "Draft version": "Draft version", "seconds": "seconds", "Remove from favorite": "Remove from favorite", "Removed from favorites!": "Removed from favorites!", "Selected Items Added to Pinned Successfully!": "Selected Items Added to Pinned Successfully!", "Pin to favorite": "Pin to favorite", "Pinned to favorites!": "Pinned to favorites!", "node_already_exists": "Obiekt o takiej nazwie {{name}} już istnieje. Spróbuj ponownie z inną nazwą!", "Please try again after sometime": "Please try again after sometime", "Node with name already exists!": "Node with name already exists!", "Please try again with different name": "Please try again with different name", "Add Folder": "Add Folder", "Edit Translation": "Edit Translation", "Add Translation": "Add Translation", "Add new language": "Add new language", "Label": "Label", "Language Code": "Language Code", "Initialize with": "Initialize with", "Required": "Required", "Are you sure you want to delete these translations?": "Are you sure you want to delete these translations?", "Confirm?": "Confirm?", "Translations Deleted Successfully!": "Translations Deleted Successfully!", "Error in deleting translations!": "Error in deleting translations!", "General Settings Published Successfully!": "General Settings Published Successfully!", "Save As": "Save As", "Filter updated successfully!": "Filter updated successfully!", "Delete?": "Delete?", "Are you sure to delete this filter?": "Are you sure to delete this filter?", "Global filters": "Global filters", "Display All": "Display All", "My filters": "My filters", "Add New": "Add New", "Display": "Display", "Hierarchy": "Hierarchy", "Object names": "Object names", "Relationship names": "Relationship names", "Object types": "Object types", "Filter saved successfully!": "Filter saved successfully!", "Add new schedule rule": "Add new schedule rule", "Color palette": "Color palette", "Theme and Layout updated successfully!": "Theme and Layout updated successfully!", "Change home layout": "Change home layout", "Reset all colors to default": "Reset all colors to default", "Reset to default": "Reset to default", "Info": "Info", "Value": "Value", "Please enter": "Please enter", "to": "to", "Tabular": "Tabular", "Add new pair": "Add new pair", "Add new node": "Add new node", "No data": "No data", "Move operation restricted!": "Move operation restricted!", "Error while moving": "Error while moving", "Please try again!": "Please try again!", "Are you sure you want to delete these selected items permanently?": "Are you sure you want to delete these selected items permanently?", "Are you sure you want to delete the item permanently": "Are you sure you want to delete the item permanently", "Delete all": "Delete all", "Multiple selected": "Multiple selected", "Unable to save an empty menu": "Unable to save an empty menu", "Settings published successfully!": "Settings published successfully!", "Home layout updated successfully!": "Home layout updated successfully!", "Default": "<PERSON><PERSON><PERSON>", "Shortcuts": "Shortcuts", "Basic": "Basic", "Trees": "Trees", "Objects": "Objects", "Attributes": "Attributes", "Add comment": "Add comment", "New translations are available!": "New translations are available!", "Lock Width": "Lock Width", "Width Saved Successfully!": "Width Saved Successfully!", "Do you want to upload them now?": "Do you want to upload them now?", "Node deleted successfully!": "Node deleted successfully!", "Cancel": "Cancel", "Browse less": "Browse less", "Browse more icons": "Browse more icons", "OR": "OR", "Created Date": "Created Date", "Alarm threshold": "Alarm threshold", "Result": "Result", "Chart": "Chart", "Error Data": "Error Data", "Test type": "Test type", "Connection": "Connection", "Responsible person": "Responsible person", "DRAG_RESTRICTED_NO_ALLOWED_CHILDREN": "{{name}} type donot have any allowed children!", "DRAG_RESTRICTED_TYPE_MISMATCHED": "{{parent}} doesnot have {{child}} as its allowed children!", "Search...": "Search...", "Background color used in homepage.": "Background color used in homepage.", "Breadcrumbs background and relations area boder color.": "Breadcrumbs background and relations area border color.", "Font color.": "Font color.", "Cards and tabs background color in homepage.": "Cards and tabs background color in homepage.", "Attributes background color": "Attributes background color", "Import now": "Import now", "Remind me later": "Remind me later", "New translations updated successfully!": "New translations updated successfully!", "Error in updating translations!": "Error in updating translations!", "Background color": "Background color", "Tile color": "Tile color", "Hide parent": "Hide parent", "Show parent": "Show parent", "Show children": "Show children", "Hide children": "Hide children", "Details": "Details", "Expand": "Expand", "Logout": "Logout", "Help": "Help", "Collapse": "Collapse", "Exit full screen": "Exit full screen", "Full screen": "Full screen", "Filter": "Filter", "Customize": "Customize", "Hide node": "Hide node", "Show hidden children": "Show hidden children", "Node added successfully!": "Node added successfully!", "Reader added successfully!": "Reader added successfully!", "Node updated successfully!": "Node updated successfully!", "Template updated successfully!": "Template updated successfully!", "Template added successfully!": "Template added successfully!", "Asset type cannot have manu as a child": "Asset type cannot have manu as a child", "Asset type menu cannot have child menus": "Asset type menu cannot have child menus", "Cannot have menu and asset type on same level": "Cannot have menu and asset type on same level", "no-repeat": "no-repeat", "Select time": "Select time", "Repeat type": "Repeat type", "Do not repeat": "Do not repeat", "Daily": "Daily", "Weekly": "Weekly", "Monthly": "Monthly", "Yearly": "Yearly", "Every weekday (Monday to Friday)": "Every weekday (Monday to Friday)", "Custom": "Custom", "Repeat every": "Repeat every", "Day": "Day", "Week": "Week", "Month": "Month", "Year": "Year", "Repeat on": "Repeat on", "Ends on": "Ends on", "Never": "Never", "On": "On", "After": "After", "occurrences": "occurrences", "at": "at", "Repeat daily from": "Repeat daily from", "Repeat weekly from": "Repeat weekly from", "Repeat monthly from": "Repeat monthly from", "Repeat yearly from": "Repeat yearly from", "Repeat custom from": "Repeat custom from", "Repeat every weekdays from": "Repeat every weekdays from", "Templates published successfully!": "Templates published successfully!", "Unable to publish templates!": "Unable to publish templates!", "Columns": "Columns", "Changes published successfully!": "Changes published successfully!", "Unable to save data!": "Unable to save data!", "Clear all": "Clear all", "Application properties": "Application properties", "Application properties updated successfully!": "Application properties updated successfully!", "Date & Time": "Date & Time", "Object Type": "Object Type", "Delete permanently": "Delete permanently", "Restore": "Rest<PERSON>", "Yes": "Yes", "No": "No", "Are you sure you want to delete": "Are you sure you want to delete", "Confirm": "Confirm", "Operation": "Operation", "Node deleted permanently!": "Node deleted permanently!", "Node restored successfully!": "Node restored successfully!", "Are you sure you want to restore": "Are you sure you want to restore", "Selected nodes deleted permanently!": "Selected nodes deleted permanently!", "Selected nodes restored permanently!": "Selected nodes restored permanently!", "Are you sure you want to delete selected nodes?": "Are you sure you want to delete selected nodes?", "Are you sure you want to restore selected nodes?": "Are you sure you want to restore selected nodes?", "Trashcan": "Trashcan", "Trash": "Trash", "Log": "Log", "Action executed successfully!": "Action executed successfully!", "Error in executing action!": "Error in executing action!", "Run connector": "Run connector", "Publish template": "Publish template", "Publish metamodel": "Publish metamodel", "Nodes already present in graph!": "Nodes already present in graph!", "Unable to add new nodes": "Unable to add new nodes", "Children of": "Children of {{name}}", "Hyperlinks of": "Hyperlinks of {{name}}", "This will remove all your custom theme colors!": "This will remove all your custom theme colors!", "Homepage background": "Homepage background", "Homepage shortcuts background": "Homepage shortcuts background", "Breadcrumbs, relations area top border": "Breadcrumbs, relations area top border", "Font & Icon Colors": "Font & Icon Colors", "Attributes background": "Attributes background", "Trash breadcrumb font color": "Trash breadcrumb font color", "Trash breadcrumb background": "Trash breadcrumb background", "Breadcrumb font color": "Breadcrumb font color", "Font color used in breadcrumbs": "Font color used in breadcrumbs", "Trashcan breadcrumbs background color": "Trashcan breadcrumbs background color", "Font color used in trashcan breadcrumbs.": "Font color used in trashcan breadcrumbs.", "New version updated!": "New version updated!", "We're excited to share the latest updates with you! We've introduced some exciting new features that we hope you'll enjoy. Please take a moment to familiarize yourself with them.": "We're excited to share the latest updates with you! We've introduced some exciting new features that we hope you'll enjoy. Please take a moment to familiarize yourself with them.", "Top features and improvements in this version": "Top features and improvements in this version", "Got it!": "Got it!", "Selected items removed from pinned successfully!": "Selected items removed from pinned successfully!", "The value of this attribute must be an ": "The value of this attribute must be an {{type}}", "integer": "integer", "decimal": "decimal", "(mockup)": "(mockup)", "Page Under Construction": "Page Under Construction", "We are working on this page. Please check back later.": "We are working on this page. Please check back later.", "More Favorites": "More Favorites", "Delete (mockup)": "Delete (mockup)", "Export (mockup)": "Export (mockup)", "Object Folder": "Object Folder", "Object Template": "Object Template", "View Details": "View Details", "Attribute Template": "Attribute Template", "Expand Here": "Expand Here", "Feature Limitation: Dragging multiple items is not supported.": "Feature Limitation: Dragging multiple items is not supported.", "Opps!": "Opps!", "Separated by commas, e.g: attr1,attr2": "Separated by commas, e.g: attr1,attr2", "Hidden Attributes (mockup)": "Hidden Attributes (mockup)", "Restore to original position": "<PERSON>ore to original position", "Are you sure you want to restore the item": "Are you sure you want to restore the item", "Are you sure you want to restore these selected items?": "Are you sure you want to restore these selected items?", "Error in restoring!": "Error in restoring!", "Move to Trashcan": "Move to Trashcan", "Node moved to trash successfully!": "<PERSON>de moved to trash successfully!", "Error in moving to trash!": "Error in moving to trash!", "Unable to move to trash!": "Unable to move to trash!", "Move selected to trash": "Move selected to trash", "Are you sure you want to move to trashcan": "Are you sure you want to move to trashcan", "Move to trash can": "Move to trash can", "Delete selected permanently": "Delete selected permanently", "No favorites": "No favorites", "Assd": "Assssddd", "Disable": "Disable", "Working version": "Working version", "Unpin selected": "<PERSON><PERSON> selected", "Download": "Download", "Create a draft version": "Create a draft version", "Error in fetching data!": "Error in fetching data!", "Close draft version": "Close draft version", "Go to draft version": "Go to draft version", "Are you sure you want to delete this draft version?": "Are you sure you want to delete this draft version?", "Delete Working Version?": "Delete Working Version?", "Draft version deleted successfully!": "Draft version deleted successfully!", "Error in deleting draft version!": "Error in deleting draft version!", "Are you sure to disable this template?": "Are you sure to disable this template?", "Template disabled successfully!": "Template disabled successfully!", "Template restored successfully!": "Template restored successfully!", "Node restored to selected successfully!": "Node restored to selected successfully!", "Restore selected": "<PERSON><PERSON> selected", "Are you sure you want to restore selected templates?": "Are you sure you want to restore selected templates?", "Are you sure to restore this template?": "Are you sure to restore this template?", "Are you sure to delete this template permanently": "Are you sure to delete this template permanently", "Are you sure to remove this menu item?": "Are you sure to remove this menu item?", "Cannot drag system nodes": "Cannot drag system nodes", "Original path:": "Original path:", "new_path_info": "New path for {{label}} will be", "Move to selected": "Move to selected", "Restore to selected": "<PERSON>ore to selected", "Node moved to selected successfully!": "<PERSON><PERSON> moved to selected successfully!", "Move to": "Move to", "Are you sure you want to move these selected items?": "Are you sure you want to move these selected items?", "Are you sure you want to move the item": "Are you sure you want to move the item", "Move": "Move", "Move selected": "Move selected", "Sibling node name must be unique! ": "Sibling node name must be unique!", "You can’t drag and drop node into the trashcan. Please use node context menu": "You can’t drag and drop node into the trashcan. Please use node context menu", "Restore and jump": "Restore and jump", "selected nodes": "selected nodes", "Draft version of disabled templates cannot be edited!": "Draft version of disabled templates cannot be edited!", "Cannot drag disabled nodes": "Cannot drag disabled nodes", "Number too long!": "Number too long!", "The value cannot have more than 12 decimal places": "The value cannot have more than 12 decimal places", "Activate": "Activate", "Template activated successfully!": "Template activated successfully!", "Activate selected": "Activate selected", "Are you sure you want to activate selected templates?": "Are you sure you want to activate selected templates?", "Are you sure to activate this template?": "Are you sure to activate this template?", "You can’t drag and drop node from the trashcan. Please use node context menu": "You can’t drag and drop node from the trashcan. Please use node context menu", "Make sure to delete assets before deleting a template!": "Make sure to delete assets before deleting a template!", "Objects with same name cannot exist in same level": "Objects with same name cannot exist in same level", "Disable selected": "Disable selected", "Cannot perform operation on attribute templates. Please use working version.": "Cannot perform operation on attribute templates. Please use working version.", "Operation restricted!": "Operation restricted!", "Your selection has read-only objects": "Your selection has read-only objects", "Are you sure you want to disable selected templates?": "Are you sure you want to disable selected templates?", "Your selection has active and disabled templates": "Your selection has active and disabled templates", "You cannot remove template which is being used. Remove assets, then try again.": "You cannot remove template which is being used. Remove assets, then try again.", "Are you sure you want to delete selected templates permanently?": "Are you sure you want to delete selected templates permanently?", "Attribute template deleted successfully!": "Attribute template deleted successfully!", "Are you sure you want to delete selected attributes templates?": "Are you sure you want to delete selected attributes templates?", "Are you sure to delete this attribute template permanently?": "Are you sure to delete this attribute template permanently?", "Template with same name already exists!": "Template with same name already exists!", "Do you want to delete whole working version?": "Do you want to delete whole working version?", "Cannot drag attribute templates!": "Cannot drag attribute templates!", "Please use draft version funtionality!": "Please use draft version funtionality!", "You can't add atribute template in a root level.": "You can't add atribute template in a root level.", "Object templates": "Object templates", "Warning": "Warning", "Objects with children should be deactivated one at a time.": "Objects with children should be deactivated one at a time.", "File must be smaller than 1MB!": "File must be smaller than 1MB!", "Change Language": "Change Language", "Get Help from here": "Get Help from here", "Feel free to begin this short tour!": "Feel free to begin this short tour!", "Welcome": "Welcome", "Languages": "Languages", "Change language from here.": "Change language from here.", "More info about CDO.tools here.": "More info about CDO.tools here.", "Expand / collapse vertical menu.": "Expand / collapse vertical menu.", "You can find more settings here.": "You can find more settings here.", "You can find deleted items here.": "You can find deleted items here.", "Do not show again": "Do not show again", "Begin Tour": "Begin Tour", "Logout here.": "Logout here.", "Know more about settings": "Know more about settings", "Metamodel management.": "Metamodel management.", "Menu management.": "Menu management.", "Your pinned items are here.": "Your pinned items are here.", "Application settings -  logo, footer, application name (…)": "Application settings -  logo, footer, application name (…)", "Add new language or update translations from here.": "Add new language or update translations from here.", "Homepage theme, color palette": "Homepage theme, color palette", "Customer category - completeness of the definition": "Customer category - completeness of the definition", "Customer - completeness of definition": "Customer - completeness of definition", "Econimic entity - completeness of definition": "Econimic entity - completeness of definition", "Person - completeness of definition": "Person - completeness of definition", "REGON - completeness of definition": "REGON - completeness of definition", "PESEL - completeness of definition": "PESEL - completeness of definition", "Company - completeness of definition": "Company - completeness of definition", "Fail": "Fail", "Success": "Success", "Assets": "Assets", "Relations": "Relations", "Delete draft version?": "Delete draft version?", "Template with same name already exists. Try with different name": "Template with same name already exists. Try with different name", "Font color used in breadcrumbs.": "Font color used in breadcrumbs.", "Do you want to delete draft version?": "Do you want to delete draft version?", "Test performance chart": "Test performance chart", "Test execution": "Test execution", "Date of execution": "Date of execution", "Number of errors [Lbr]": "Number of errors [Lbr]", "Number of Tested [Lpr]": "Number of Tested [Lpr]", "Show SQL": "Show SQL", "SQL Query": "SQL Query", "Do not sort": "Do not sort", "Show selected first": "Show selected first", "View in trashcan": "View in trashcan", "Allowed Children": "Allowed Children", "Icon": "Icon", "Atribute Type": "Atribute Type", "Is mandatory": "Is mandatory", "Compound": "Compound", "Data": "Data", "Date Time": "Date Time", "Decimal": "Decimal", "Dropdown List Mono": "Dropdown List Mono", "Dropdown List Multi": "Dropdown List Multi", "HTML Text": "HTML Text", "IFrame": "IFrame", "Life Cycle": "Life Cycle", "Link": "Link", "Number": "Number", "Plain Text": "Plain Text", "Switch": "Switch", "Time": "Time", "Regex": "Regex", "Filter Menu": "<PERSON><PERSON>", "Filter Template": "Filter <PERSON>late", "Source Data": "Source Data", "Name of List 1": "Name of List 1", "Name of List 2": "Name of List 2", "Multiplicity for List 1": "Multiplicity for List 1", "Multiplicity for List 2": "Multiplicity for List 2", "Multiplicity": "Multiplicity", "Attributes Lookup": "Attributes <PERSON><PERSON>", "List of Values": "List of Values", "Dropdown Items": "Dropdown Items", "No assets": "No assets", "No history": "No history", "No logs": "No logs", "No relations": "No relations", "Actions": "Actions", "Visualization": "Visualization", "Simple view": "Simple view", "Enriched view": "Enriched view", "Multiplicity value mismatch!": "Multiplicity value mismatch!", "Error occurred in fetching node details!": "Error occurred in fetching node details!", "Source": "Source", "Parent of list 1": "Parent of list 1", "Parent of list 2": "Parent of list 2", "Preview of list 1": "Preview of list 1", "Preview of list 2": "Preview of list 2", "Node templates 1": "Node templates 1", "Node templates 2": "Node templates 2", "Sort alphabetically ascending": "Sort alphabetically ascending", "Sort alphabetically descending": "Sort alphabetically descending", "Sort by visual order": "Sort by visual order", "Before change": "Before change", "Date of change": "Date of change", "Mark as not found": "<PERSON> as not found", "Suspects updated successfully!": "Suspects updated successfully!", "Are you sure you want to mark the selected nodes as not found?": "Are you sure you want to mark the selected nodes as not found?", "Are you sure to delete this attachment?": "Are you sure to delete this attachment?", "Attachment deleted successfully!": "Attachment deleted successfully!", "SEARCH RESULTS": "SEARCH RESULTS", "View all": "View all", "Commented in": "Commented in", "Readers": "Readers", "Authors": "Authors", "Group": "Group", "Person": "Person", "Group of Readers": "Group of Readers", "Group of Authors": "Group of Authors", "New": "New", "Convert": "convert", "Add to group": "Add to group", "Convert to author": "Convert to author", "Convert to readers": "Conver to readers", "Are you sure you want to convert selected?": "Are you sure you want to convert selected?", "Are you sure you want to delete selected?": "Are you sure you want to delete selected?", "Modified": "Modified", "Deleted": "Deleted", "No Data": "No Data", "Scheduler": "Scheduler", "Dropdown List": "Dropdown List", "Short Text": "Short Text", "Application Properties": "Application Properties", "No attachments": "No attachments", "No comments": "No comments", "Activation": "Activation", "Duration": "Duration", "Status": "Status", "Change Password": "Change Password", "Current Password": "Current Password", "New Password": "New Password", "Password must be at least 8 characters": "Password must be at least 8 characters", "Confirm Password": "Confirm Password", "Passwords must match!": "Passwords must match!", "Change": "Change", "Date of test": "Date of test", "Not started": "Not started", "Warning threshold": "Warning threshold", "Error": "Error", "error.generic": "Sorry, something went wrong. Please try again.", "Add attachment": "Add attachment", "Attachment uploaded successfully!": "Attachment uploaded successfully!", "Are you sure to delete this record?": "Are you sure to delete this record?", "Record deleted successfully!": "Record deleted successfully!", "Error in deletion!": "Error in deletion!", "Icons": "Icons", "Attachment downloaded successfully!": "Attachment downloaded successfully!", "Start at": "Start at", "Repeat": "Repeat", "Until": "Until", "Set by": "Set by", "Date'n'time": "Date'n'time", "one time": "one time", "Select": "Select", "Cron expression": "Cron expression", "No schedules!": "No schedules!", "Set Permissions": "Set Permissions", "No permissions": "No permissions", "Are you sure to delete this permisison?": "Are you sure to delete this permisison?", "Permission deleted successfully!": "Permission deleted successfully!", "Group deleted successfully!": "Group deleted successfully!", "Group added successfully!": "Group added successfully!", "Group updated successfully!": "Group updated successfully!", "Permissions": "Permissions", "Minutes": "Minutes", "Hourly": "Hourly", "Every": "Every", "minute(s)": "minute(s)", "hour(s)": "hour(s)", "day(s)": "day(s)", "Every weekday": "Every weekday", "of every month": "of every month", "Last day of every month": "Last day of every month", "On the last weekday of every month": "On the last weekday of every month", "days before the end of the month": "days before the end of the month", "Days of every month": "Days of every month", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday", "Hint:": "Hint:", "Cron format": "Cron format", "minute hour day month weekday": "minute hour day month weekday", "Every weekday at 9:00 AM": "Every weekday at 9:00 AM", "Every 30 minutes": "Every 30 minutes", "First day of every month at midnight": "First day of every month at midnight", "Subscribe": "Subscribe", "Unsubscribe": "Unsubscribe", "Do you want to subscribe for its children?": "Do you want to subscribe for its children?", "Subscribed successfully!": "Subscribed successfully!", "Frequency updated successfully!": "Frequency updated successfully!", "Newsletter": "Newsletter", "Frequency": "Frequency", "Once a day": "Once a day", "Once a week": "Once a week", "Permissions cleared successfully!": "Permissions cleared successfully!", "Comment added successfully!": "Comment added successfully!", "Are you sure to delete this comment?": "Are you sure to delete this comment?", "Comment deleted successfully!": "Comment deleted successfully!", "Comment edited successfully!": "Comment edited successfully!", "Edit comment": "Edit comment", "No Suspects": "No Suspects", "You do not have permission to view the characteristics": "You do not have permission to view the characteristics", "EX_DEFAULT": "Unexpected error. See tec. log: {{exceptionGUID}}.", "EX_INIT_DEFAULT": "Unexpected error during application startup.See tec. log: {{exceptionGUID}}.", "EX_DUPLICATE_TEMPLATE_NAMES": "Names must not be repeted. See tec. log: {{exceptionGUID}}.", "EX_UNSUPPORTED_PROFILE_ID": "User profile configuration error. See tec. log: {{exceptionGUID}}.", "EX_PERM_ACTION_NOT_ALLOWED": "No entitlement to {{actionName}} on Asset ID {{nodeId}}.", "EX_DAO_ATTRIBUTE": "Technical error. See tec. log: {{exceptionGUID}}.", "EX_LUCENE_ANALYZER_TYPE": "Lucene - analyser {{analyzer}} does not exist.", "EX_FILE_DATA_NOT_FOUND": "File not found. See tec. log: {{exceptionGUID}}.", "EX_FILE_EXTENSIONS": "Not allowed file type. You can attach: {{allowedExtensions}}.", "EX_FILE_EXISTS_ICON": "A file with this name already exists.", "EX_ASSET_NOT_EXISTS_ERROR": "Asset with ID {{nodeId) does not exist.", "EX_ASSET_PARENT_ID": "Technical error. See tec. log: {{exceptionGUID}}.", "EX_ASSET_PARENT_NOT_FOUND": "Technical error. See tec. log: {{exceptionGUID}}.", "EX_ASSET_PARENT_ORGINAL": "The original parent object does not exist.", "EX_ASSET_DELETE": "You cannot delete an object that has subordinate objects. ", "EX_ASSET_SIBLINGS_UNIQUE": "Names must not be repeated.", "EX_ASSET_METAMODEL_FOLDER_DELETE": "You cannot delete a folder if it is not empty.", "EX_SEC_LOGIN_ERROR": "Error while trying to log in. See tec. log: {{exceptionGUID}}.", "EX_SEC_GENERAL_ERROR": "Security error. See tec. log: {{exceptionGUID}}.", "EX_SEC_SESSION_EXPIRED": "The session has expired. See tec. log: {{exceptionGUID}}.", "EX_SEC_INVALID_TOKEN": "Incorrect token. See tec. log: {{exceptionGUID}}.", "EX_SEC_NOT_AUTHENTICATED": "Lack of authentication. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_ERROR": "Connector error. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_MAPPER_ERROR": "Mapper error. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_CONNECTION_OPEN_ERROR": "Error during connection establishment. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_CONNECTION_CLOSE_ERROR": "Error while closing the connection. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_CONNECTION_TEST_ERROR": "Error during connection test. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_CONNECTION_ERROR": "Connection cannot be established. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_KEYS_NOT_UNIQUE": "Input data key uniqueness error. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_KEYS_CHECK_ERROR": "Key error in input data. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_EXTRACTOR_ERROR": "Data download error. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_PROCESS_ERROR": "Data processing error. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_PARAMETERS_ERROR": "Incorrect connector parameters. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_APPROVAL_ERROR": "Changes cannot be saved. See tec. log: {{exceptionGUID}}.", "EX_CONNECTOR_TIMEOUT_CONNECTION_ERROR": "Connection waiting timeout has been exceeded.", "EX_CONNECTOR_TIMEOUT_CONNECTION_TEST_ERROR": "Connection test timeout has been exceeded.", "EX_CONNECTOR_TIMEOUT_EXTRACTOR_ERROR": "Extractor timeout has been exceeded.", "EX_CONNECTOR_TIMEOUT_PROCESS_ERROR": "Processing timeout has been exceeded.", "EX_CONNECTOR_KEY_TYPE_NOT_SUPPORT": "Key type “{{keyType}}” is not supported.", "EX_CONNECTOR_STATUS_NOT_READY": "You can't run a connector that has a status of {{status}}.", "EX_CONNECTOR_SUSPECTED": "There are still assets awaiting decision.", "EX_PASSWORD_READ_ERROR": "Technical error. See tec. log: {{exceptionGUID}}.", "OP_ADD_NODE": "Object {{nodeName}} added in {{parentId}} branch.", "OP_MOVE_NODE": "Object moved from {{oldParentId}} branch to {{newParentId}} branch.", "OP_DELETE_NODE": "Asset removed.", "OP_RENAME_NODE": "Name changed.", "OP_EDIT_ATTRIBUTE": "Characteristics changed.", "OP_CHANGE_VISUAL_ORDER": "Visual order changed.", "OP_MOVE_TO_TRASH": "Asset moved to trash.", "OP_RESTORE_FROM_TRASH": "Asset restored from trash.", "OP_CONNECTOR_START": "Connector has started work.", "OP_CONNECTOR_END": "Connector has completed work.", "OP_CONNECTOR_ERROR": "Connector error: {{exceptionMessage}}.", "OP_CONNECTOR_NOT_READY": "Connector is not ready for launch.", "OP_CONNECTOR_CANCEL": "Connector operations has stopped.", "OP_DQM_TEST_START": "DQM Test launched.", "OP_DQM_TEST_END": "DQM Test completed.", "OP_DQM_TEST_ERROR": "DQM Test error {{exceptionessage}}.", "EX_OBJECT_OPERATION_NOT_ALLOWE": "You do not have permission to perform this operation.", "Date & time": "Date & time", "Manual": "Manual", "Refresh": "Refresh", "Permissions set for this Asset": "Permissions set for this Asset", "Permissions inherited": "Permissions inherited", "Sort": "Sort", "Error in fetching data": "Error in fetching data", "Hide Referenced": "<PERSON><PERSON> Referenced", "Show referenced": "Show referenced", "Sorry, you are not authorized to access this page.": "Sorry, you are not authorized to access this page.", "Trash bin": "Trash bin", "Are you sure you want to move it to trash bin?": "Are you sure you want to move it to trash bin?", "allowed file types": "allowed file types", "Illegal file type": "Illegal file type", "All attachments uploaded successfully": "All attachments uploaded successfully", "attachments uploaded successfully": "attachments uploaded successfully", "Error in uploading attachment": "Error in uploading attachment", "A file with this name already exists": "A file with this name already exists", "Delete this attachment?": "Delete this attachment?", "Clear?": "Clear?", "Remove all attachments?": "Remove all attachments?", "Remove": "Remove", "Uploading": "Uploading", "Uploading...": "Uploading...", "Clear Failed": "Clear Failed", "Clear": "Clear", "Cancel Upload": "Cancel Upload", "Cancel upload?": "Cancel upload?", "Cancel?": "Cancel?", "Cancel remaining uploads?": "Cancel remaining uploads?", "Upload cancelled": "Upload cancelled", "Upload cancelled with {{count}} files uploaded": "Upload cancelled with {{count}} files uploaded", "Uploaded": "Uploaded", "Failed": "Failed", "Cannot remove during upload": "Cannot remove during upload", "Be minded that the entire branch will be moved": "Be minded that the entire branch will be moved", "Define allowed children types": "Define allowed children types", "Allowed children": "Allowed children", "No results found!": "No results found!", "These name characters are prohibited:": "These name characters are prohibited:", "There is no working copy of this node": "There is no working copy of this node", "Please fill all mandatory fields": "Please fill all mandatory fields", "Add Reader": "Add Reader", "Relation no candidates": "According to the current metamodel definition you can point node templates: {{templates}}. If you can't select any objects, it means you don't have permissions."}