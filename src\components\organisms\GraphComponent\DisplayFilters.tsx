import { styled } from "@linaria/react";
import { Checkbox } from "antd";
import { useTranslation } from "react-i18next";

const CheckboxGroup = Checkbox.Group;

const DisplayFilters = ({ selectedFilters, setSelectedFilters }) => {
  const { t } = useTranslation();

  const DISPLAY_OPTIONS = [
    {
      label: t("Object names"),
      value: "object-names",
    },
    {
      label: t("Relationship names"),
      value: "relations-name",
    },
    {
      label: t("Orphans"),
      value: "orphans",
    },
  ];

  const handleCheckboxChange = (checked, value) => {
    const newSelectedValues = [...selectedFilters.display];
    const index = newSelectedValues.indexOf(value);
    if (checked) {
      if (index === -1) newSelectedValues.push(value);
    } else {
      if (index > -1) newSelectedValues.splice(index, 1);
    }
    setSelectedFilters({
      ...selectedFilters,
      display: newSelectedValues,
    });
  };

  return (
    <Wrapper>
      <div className="title">{t("Display")}</div>
      <CheckboxGroup value={selectedFilters.display}>
        {DISPLAY_OPTIONS.map((option) => (
          <div key={option.value} style={{ display: "inline-block" }}>
            <Checkbox
              value={option.value}
              onChange={(e) =>
                handleCheckboxChange(e.target.checked, option.value)
              }
              onClick={(e) => e.stopPropagation()}
            />
            <span
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
              }}
              style={{ cursor: "pointer", marginLeft: 7 }}
            >
              {option.label}
            </span>
          </div>
        ))}
      </CheckboxGroup>
    </Wrapper>
  );
};

export { DisplayFilters };

const Wrapper = styled.div`
  margin-bottom: 40px;

  & .ant-checkbox-group {
    display: flex;
    flex-direction: column;
    padding-left: 21px;
    margin-top: 12px;

    & > label {
      margin-left: 0px !important;
      font-size: 13px;
    }
  }

  & .title {
    background-color: #ccdaec;
    padding: 3px 10px;
    color: #4277a2;
  }
`;
