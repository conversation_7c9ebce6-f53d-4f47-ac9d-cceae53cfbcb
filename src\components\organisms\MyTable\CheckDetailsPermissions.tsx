import { useEffect, useRef } from "react";
import { usePermissions } from "../../../utils/functions/customHooks";
import { DetailCellRenderer } from "./DetailCellRenderer";

const CheckDetailsPermissions = (props) => {
  const { data, node, context, api } = props;
  const { getPermissions } = usePermissions();
  const permissions = getPermissions(data?.permissionsId || 0);
  const hasTriggeredPopup = useRef(false);

  useEffect(() => {
    if (!permissions.includes("VIEW") && !hasTriggeredPopup.current) {
      hasTriggeredPopup.current = true;
      api?.setRowNodeExpanded(node, false, true);
      context.setNoPermissionPopup(node);
    }
  }, [permissions, node, context, api]);

  if (permissions.includes("VIEW")) {
    return <DetailCellRenderer data={data} />;
  }

  return <></>;
};

export { CheckDetailsPermissions };
